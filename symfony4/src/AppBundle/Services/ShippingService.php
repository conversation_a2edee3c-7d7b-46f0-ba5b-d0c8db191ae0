<?php

namespace AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Entity\Country;
use AppBundle\Entity\User;
use AppBundle\Model\CarrierOffer;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\CartMerchantShippingOptions;
use AppBundle\Model\CartShippingOption;
use AppBundle\Model\GroupOfPackages;
use AppBundle\Model\Offer;
use AppBundle\Util\DateUtil;
use AppBundle\Model\ShippingOption;
use AppBundle\Repository\CountryRepository;
use ArrayIterator;
use DateTime;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class ShippingService implements LoggerAwareInterface
{
    private OfferService $offerService;
    private UpelaApiService $upelaApiService;
    private LoggerInterface $logger;
    private RedisService $cache;
    private SerializerService $serializer;
    private CountryRepository $countryRepository;
    private CartApi $cartApi;
    private CustomsService $customsService;
    private ProductOfferApi $productOfferApi;
    private int $shippingProductId;
    private AlstomCustomAttributes $customAttributes;
    private FeesOperatorShippingService $feesOperatorShippingService;

    private const CART_MERCHANT_SHIPPING_OPTIONS = 'CART_MERCHANT_SHIPPING_OPTIONS';

    public function __construct(
        OfferService $offerService,
        UpelaApiService $upelaApiService,
        RedisService $cache,
        SerializerService $serializer,
        CountryRepository $countryRepository,
        CartApi $cartApi,
        CustomsService $customsService,
        ProductOfferApi $productOfferApi,
        AlstomCustomAttributes $customAttributes,
        FeesOperatorShippingService $feesOperatorShippingService
    )
    {
        $this->offerService = $offerService;
        $this->upelaApiService = $upelaApiService;
        $this->cache = $cache;
        $this->serializer = $serializer;
        $this->countryRepository = $countryRepository;
        $this->cartApi = $cartApi;
        $this->customsService = $customsService;
        $this->productOfferApi = $productOfferApi;
        $this->customAttributes = $customAttributes;
        $this->feesOperatorShippingService = $feesOperatorShippingService;
    }

    public function buildCartMerchantShippingOptionsV2(Cart $cart, User $user, bool $resetCache = false): ?CartShippingOption
    {
        $this->logger->info(
            'ShippingService::buildCartMerchantShippingOptionsV2',
            LogUtil::buildContext([
                'cartID' => $cart->getId(),
            ])
        );

        $shippingOptions = [];

        /** @var CartMerchant $merchant */
        foreach ($cart->getMerchants() as $merchant) {
            $groupOfPackages = $this->packMerchantCartV2($merchant, $cart, $user);
            $shippingOption = $this->computeCartMerchantShippingOptionsV2($merchant, $cart, $groupOfPackages, $user->getCompany());
            if ($shippingOption) {
                $this->logger->info(
                    'ShippingService::buildCartMerchantShippingOptionsV2',
                    LogUtil::buildContext([
                        'merchantID' => $merchant->getId(),
                        'shippingOption' => $shippingOption,
                    ])
                );
                $shippingOptions[] = $shippingOption;
            }
        }

        $cartShippingOption = new CartShippingOption();
        $cartShippingOption->setCartId($cart->getId());
        $cartShippingOption->setCartMerchantShippingOptions($shippingOptions);
        $cartShippingOption->setCurrency($cart->getCurrency());

        return $cartShippingOption;
    }

    public function buildCartMerchantShippingOptions(Cart $cart, User $user, bool $resetCache = false): ?CartShippingOption
    {
        $shippingOptions = [];
        $shippingOptionsJson = null;

        // ONLY EURO CARTS IS HANDLE BY UPELA
        if ($cart->getCurrency() !== 'EUR') {
            return null;
        }

        /** @var CartMerchant $merchant */
        foreach ($cart->getMerchants() as $merchant) {
            if (!$merchant->isUpelaActive()) {
                continue;
            }

            $cacheKey = $this->buildCacheKey($user->getId(),$cart->getId(),$merchant->getId(),$cart->getAddress()->getId());
            if($resetCache) $this->cache->removeItem($cacheKey);

            $shippingOptionsJson = $this->cache->getItem($cacheKey);
            if ($shippingOptionsJson !== null) {
                $shippingOptions[] = $this->serializer->deserialize($shippingOptionsJson, CartMerchantShippingOptions::class);
            } else {
                $groupOfPackages = $this->packMerchantCart($merchant, $cart, $user);
                $shippingOption = $this->computeCartMerchantShippingOptions($merchant, $cart, $groupOfPackages, $user->getCompany());

                if ($shippingOption) {
                    $shippingOptionsJson = $this->serializer->serialize($shippingOption);
                    $this->cache->saveItem($cacheKey, $shippingOptionsJson, 200);

                    $shippingOptions[] = $shippingOption;
                }
            }
        }

        $cartShippingOption = new CartShippingOption();
        $cartShippingOption->setCartId($cart->getId());
        $cartShippingOption->setCartMerchantShippingOptions($shippingOptions);
        $cartShippingOption->setCurrency($cart->getCurrency());

        return $cartShippingOption;
    }

    public function saveSelectedMerchantShippingOptionsV2(CartShippingOption $cartShippingOptions)
    {
        $data = [];

        /** @var CartMerchantShippingOptions $merchantShippingOption */
        foreach ($cartShippingOptions->getCartMerchantShippingOptions() as $merchantShippingOption) {
            $merchantId = $merchantShippingOption->getMerchantId();
            $shippingOption = $merchantShippingOption->getFastestOption();

            /** @var CarrierOffer $carrierOffer */
            $i = 0;
            foreach ($shippingOption->getCarrierOffers() as $carrierOffer) {
                $i+=1;
                $shippingOptionMode = "shipping option $i";
                $cartItems = [];
                $itemsQte = $carrierOffer->getItemsQte();
                foreach($carrierOffer->getCartItems() as $key => $cartItem) {
                    if(!empty($itemsQte[$key])) {
                        $qte = $itemsQte[$key];
                    } else {
                        $qte = $cartItem->getQuantity();
                    }
                    $qte = $itemsQte[$key];
                    $cartItems[] = ["resource_uri" => "/v1/cart_item/" . $cartItem->getId() . "/",
                            "quantity" => (int)$qte];
                }

                $data[] = [
                    "amount_with_vat" => 0,
                    "amount_without_vat" => 0,
                    "carrier" => '/v1/carrier/101/', // @TODO
                    "cart_items" => $cartItems,
                    "delivery_within_hours" => 0,
                    "collection_within_hours" => 0,
                    "merchant" => "/v1/merchant/" . $merchantId . "/",
                    "vat_amount" => 0,
                    "vat_rate" => 0,
                    "name" => $shippingOptionMode,
                    "options" => [
                        "offer" => [
                            'id' => $carrierOffer->getId(),
                            'carrierCode' => $carrierOffer->getCarrierCode(),
                            'carrierName' => $carrierOffer->getCarrierName(),
                            'serviceCode' => $carrierOffer->getServiceCode(),
                            'serviceName' => $carrierOffer->getServiceName(),
                            'vatRate' => $carrierOffer->getVatRate(),
                            'priceTe' => $carrierOffer->getPriceTe(),
                            'upelaPriceTe' => $carrierOffer->getUpelaPriceTe(),
                            'currency' => $cartShippingOptions->getCurrency(),
                            'priceTi' => $carrierOffer->getPriceTi(),
                            'upelaPriceTi' => $carrierOffer->getUpelaPriceTi(),
                            'shipmentDate' => $carrierOffer->getShipmentDate(),
                            'deliveryDate' => $carrierOffer->getDeliveryDate(),
                            'carrierId' => $carrierOffer->getCarrierIdentifier(),
                            'countryCode' => $carrierOffer->getGroupOfPackages()->getCountryCode(),
                            'parcelType' => $carrierOffer->getGroupOfPackages()->getShipmentType(),
                            'city' => $carrierOffer->getGroupOfPackages()->getTown(),
                        ],
                        "parcels" => $carrierOffer->getGroupOfPackages()->getPackages(),
                        "shipmentId" => $carrierOffer->getShipmentId(),
                    ]
                ];
            }
        }

        $this->logger->info(
            'ShippingService::saveSelectedMerchantShippingOptionsV2',
            LogUtil::buildContext([
                'cartID' => $cartShippingOptions->getCartId(),
                'data' => $data
            ])
        );

        if (count($data)) {
            $reponse = $this->cartApi->createCartShipping($cartShippingOptions->getCartId(), $data);
            $this->logger->info(
                'ShippingService::saveSelectedMerchantShippingOptionsV2 - cartApi->createCartShipping',
                LogUtil::buildContext([
                    'cartID' => $cartShippingOptions->getCartId(),
                    'data' => $data,
                    'reponse' => $reponse
                ])
            );
        }
    }

    /**
     * Save shipping option selected by the buyer in izberg
     * The objects saved will be reused when creating the upela preorder
     *
     * @param CartShippingOption $cartShippingOptions
     * @param array $selectedShippingOptions
     */
    public function saveSelectedMerchantShippingOptions(CartShippingOption $cartShippingOptions, array $selectedShippingOptions)
    {
        $data = [];

        foreach ($selectedShippingOptions as $merchantId => $shippingOptionMode) {
            /** @var ShippingOption $shippingOption */
            $shippingOption = $cartShippingOptions->findMerchantShippingOption($merchantId, $shippingOptionMode);

            if (!$shippingOption) {
                continue;
            }

            if (preg_match('/mono_[0-9]+/', $shippingOptionMode)) {
                $shippingOptionMode = $shippingOption->getFirstServiceName();
            }

            /** @var CarrierOffer $carrierOffer */
            foreach ($shippingOption->getCarrierOffers() as $carrierOffer) {
                $cartItems = array_map(
                    function (CartItem $cartItem) {
                        return [
                            "resource_uri" => "/v1/cart_item/" . $cartItem->getId() . "/",
                            "quantity" => $cartItem->getQuantity()
                        ];
                    },
                    $carrierOffer->getCartItems()
                );

                $data[] = [
                    "amount_with_vat" => 0,
                    "amount_without_vat" => 0,
                    "cart_items" => $cartItems,
                    "delivery_within_hours" => 0,
                    "collection_within_hours" => 0,
                    "merchant" => "/v1/merchant/" . $merchantId . "/",
                    "vat_amount" => 0,
                    "vat_rate" => 0,
                    "name" => $shippingOptionMode,
                    "options" => [
                        "offer" => [
                            'id' => $carrierOffer->getId(),
                            'carrierCode' => $carrierOffer->getCarrierCode(),
                            'carrierName' => $carrierOffer->getCarrierName(),
                            'serviceCode' => $carrierOffer->getServiceCode(),
                            'serviceName' => $carrierOffer->getServiceName(),
                            'vatRate' => $carrierOffer->getVatRate(),
                            'priceTe' => $carrierOffer->getPriceTe(),
                            'upelaPriceTe' => $carrierOffer->getUpelaPriceTe(),
                            'currency' => $cartShippingOptions->getCurrency(),
                            'priceTi' => $carrierOffer->getPriceTi(),
                            'upelaPriceTi' => $carrierOffer->getUpelaPriceTi(),
                            'shipmentDate' => $carrierOffer->getShipmentDate(),
                            'deliveryDate' => $carrierOffer->getDeliveryDate(),
                            'carrierId' => $carrierOffer->getCarrierIdentifier(),
                            'countryCode' => $carrierOffer->getGroupOfPackages()->getCountryCode(),
                            'parcelType' => $carrierOffer->getGroupOfPackages()->getShipmentType(),
                            'city' => $carrierOffer->getGroupOfPackages()->getTown(),
                        ],
                        "parcels" => $carrierOffer->getGroupOfPackages()->getPackages(),
                        "shipmentId" => $carrierOffer->getShipmentId(),
                    ]
                ];
            }
        }

        if (count($data)) {
            $this->cartApi->createCartShipping($cartShippingOptions->getCartId(), $data);
        }
    }

    public function createShippingOffer(
        int $merchantId,
        float $priceTe,
        string $shipmentId,
        string $city,
        DateTimeImmutable $shipmentDate,
        int $parcelType
    ): int
    {
        $shippingTypeAvailable = [
            UpelaApiService::PARCEL_SHIPMENT_TYPE => 'parcel',
            UpelaApiService::PALLET_SHIPMENT_TYPE => 'pallet',
        ];

        $name = sprintf(
            'shipping %s - %s - %s',
            $city,
            $shipmentDate->format('d/m/Y'),
            $shippingTypeAvailable[$parcelType] ?? ''
        );

        $offer = $this->productOfferApi->createProductOffer(
            $this->shippingProductId,
            $merchantId,
            $name,
            $priceTe,
            sprintf('shipment-%s', $shipmentId)
        );

        $this->productOfferApi->activateProduct($offer->getId());

        return $offer->getId();
    }

    /**
     * @param OrderMerchant[] $merchantOrders
     * @return bool
     */
    public function removeShippingOfferFromMerchantOrders(array $merchantOrders): bool
    {
        $shippingOfferIds = [];

        foreach($merchantOrders as $merchantOrder) {
            $merchantOrderItems = $merchantOrder->getItems() ?? [];

            $shippingOrderItems = array_filter(
                $merchantOrderItems->toArray(),
                function(OrderItem $merchantOrderItem) {
                    return (preg_match('/^shipment-[0-9]+$/', $merchantOrderItem->getOfferExternalId()));
                }
            );

            $shippingOfferIds = $shippingOfferIds + array_map(
                    function(OrderItem $merchantOrderItem) {
                        return $merchantOrderItem->getOfferId();
                    },
                    $shippingOrderItems
                );
        }

        foreach ($shippingOfferIds as $shippingOfferId) {
            try {
                $this->productOfferApi->deactivateProduct($shippingOfferId);
                $this->productOfferApi->trashProduct($shippingOfferId);
                $this->productOfferApi->removeOfferProduct($shippingOfferId);
            } catch(Exception $e) {
                $this->logger->error("unable to remove shipping offer from Izberg : ".$e->getMessage(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR,
                        'shippingOfferId' => $shippingOfferId
                    ]));
            }
        }

        return true;
    }

    /**
     * @param int $offerPreparationPeriod Delivery time set for a given offer in izberg (in days)
     * @return DateTimeImmutable
     * @throws Exception
     */
    private function computeMerchantPreparationDate(int $offerPreparationPeriod): DateTimeImmutable
    {
        $date = DateTimeImmutable::createFromFormat(
            'Y-m-d H:i:s',
            (new DateTimeImmutable())->format('Y-m-d') . ' 00:00:00'
        );

        // compute the expected shipment date based on the offer delivery time property
        $modifier = '+' . $offerPreparationPeriod . 'day';
        $date = $date->modify($modifier);

        return DateUtil::moveToNextWorkingDay($date);
    }

    private function packMerchantCartV2(CartMerchant $merchant, Cart $cart, User $user): array
    {
        $groupOfPackage = [];
        $groupOfPackages = [];

        $dateToday = DateTimeImmutable::createFromFormat(
            'Y-m-d H:i:s',
            (new DateTimeImmutable())->format('Y-m-d') . ' 00:00:00'
        );
        $shippingType = UpelaApiService::PARCEL_SHIPMENT_TYPE; // @TODO

        /**  @var CartItem $item */
        foreach ($merchant->getItems() as $key => $item) {
            $offer = $item->getOffer();
            $itemExtraInfos = $item->getExtraInfo(); // [Cart-item-split-delivery] => Array([demand_delivery] => Array([date] => 02/12/2025[quantity] => 10) [on_stock_delivery] => Array([date] => 02/05/2025[quantity] => 20))

            $deliveriesToProcess = [];

            if(!empty($itemExtraInfos['Cart-item-split-delivery']) && count($itemExtraInfos['Cart-item-split-delivery'])>0) {
                foreach($itemExtraInfos['Cart-item-split-delivery'] as $type => $splitDelivery) {
                    $qte = $splitDelivery['quantity'];
                    if($type == 'on_stock_delivery') {
                        $dateExpedition = $dateToday->modify('+' . $item->getDeliveryTimeDelayBeforeShipping() . 'day');
                    } else {
                        $dateExpedition = $dateToday->modify('+' . $item->getDeliveryTime() . 'day');
                    }
                    $deliveriesToProcess[] = ['qte' => $qte, 'dateExpedition' => $dateExpedition];
                }
            } else {
                if($offer->getStockAvailability() == 'on_stock') {
                    $dateExpedition = $dateToday->modify('+' . $item->getDeliveryTimeDelayBeforeShipping() . 'day');
                } else {
                    $dateExpedition = $dateToday->modify('+' . $item->getDeliveryTime() . 'day');
                }
                $deliveriesToProcess[] = ['qte' => $item->getQuantity(), 'dateExpedition' => $dateExpedition];
            }

            foreach($deliveriesToProcess as $deliveryToProcess) {
                $dateExpeditionObj = DateTime::createFromImmutable($deliveryToProcess['dateExpedition']);
                $weekNumber = $dateExpeditionObj->format('W');
                $year = $dateExpeditionObj->format('Y');
                $weekKey = $year . str_pad($weekNumber, 2, '0', STR_PAD_LEFT);

                $preparationdDate = $this->computeMerchantPreparationDate($item->getDeliveryTime());
                $deliveryDate = $preparationdDate->format('Y-m-d');

                $packages = $this->computePackage($offer, $deliveryToProcess['qte']);

                $hash = $weekKey;
                $this->logger->info(
                    'ShippingService::packMerchantCartV2 - item',
                    LogUtil::buildContext([
                        'itemID' => $item->getId(),
                        'itemName' => $item->getName(),
                        'offerStockAvailability' => $offer->getStockAvailability(),
                        'itemDelayBeforeShipping' => $item->getDeliveryTimeDelayBeforeShipping(),
                        'itemTotalelayorustomer' => $item->getDeliveryTime(),
                        'itemExtraInfos' => $item->getExtraInfo(),
                        'itemQuantity' => $item->getQuantity(),
                        'deliveryQte' => $deliveryToProcess['qte'],
                        'preparationDate' => $preparationdDate,
                        'key' => $weekKey,
                        'hash' => $hash,
                        'packages' => $packages
                    ])
                );

                /** @var Country $country */
                $country = $this->countryRepository->findOneBy(['izbFcaCountry' => $offer->getIncotermCountry()]);
                $countryCode = '';
                if (!is_null($country)) {
                    $countryCode = strtoupper($country->getIzbergCode());
                }

                $groupOfPackages[$hash]['item'][] = $item;
                $groupOfPackages[$hash]['itemQte'][] = $deliveryToProcess['qte'];

                $groupOfPackages[$hash]['offer'][] = $item;

                if(!empty($groupOfPackages[$hash]['deliveryDate'])) {
                    $deliveryDateObj1 = new DateTime($groupOfPackages[$hash]['deliveryDate']);
                    $deliveryDateObj2 = new DateTime($deliveryDate);
                    if($deliveryDateObj2 > $deliveryDateObj1) {
                        $groupOfPackages[$hash]['deliveryDate'] = $deliveryDateObj2;
                    }
                } else {
                    $groupOfPackages[$hash]['deliveryDate'] = $deliveryDate;
                }
                $groupOfPackages[$hash]['street'] = $offer->getFcaAddress();
                $groupOfPackages[$hash]['zipCode'] = $offer->getFcaZipCode();
                $groupOfPackages[$hash]['town'] = $offer->getFcaZipTown();
                $groupOfPackages[$hash]['countryCode'] = $countryCode;
                $groupOfPackages[$hash]['shippingType'] = $shippingType;
                if (!empty($packages)) {
                    if (count($packages) > 1) {
                        foreach ($packages as $element) {
                            $groupOfPackages[$hash]['result'][] = $element;
                        }
                    } else {
                        $groupOfPackages[$hash]['result'][] = array_shift($packages);
                    }
                }
                if(!isset($groupOfPackages[$hash]['result'])) {
                    $groupOfPackages[$hash]['result'] = [];
                }
            }
        }

        $shippingAddress = $cart->getAddress();
        $companyName = $user->getCompany()->getName();

        foreach ($groupOfPackages as $key => $group) {
            $this->logger->info(
                'ShippingService::packMerchantCartV2 - groupOfPackages',
                LogUtil::buildContext([
                    'key' => $key,
                    'group' => $group
                ])
            );
            $groupOfPackage[$key] = (new GroupOfPackages())
                ->setShipmentDate($group['deliveryDate'])
                ->setMerchantId($merchant->getId())
                ->setMerchantName($merchant->getName())
                ->setMerchant($merchant)
                ->setUser($user)
                ->setCompanyName($companyName)
                ->setCountryCode($group['countryCode'])
                ->setStreet($group['street'])
                ->setTown($group['town'])
                ->setZipCode($group['zipCode'])
                ->setPackages($group['result'])
                ->setCartItems($group['item'])
                ->setItemsQte($group['itemQte'])
                ->setShippingAddress($shippingAddress)
                ->setShipmentType($group['shippingType']);
        }

        ksort($groupOfPackage, SORT_NUMERIC);

        return $groupOfPackage;
    }

    private function packMerchantCart(CartMerchant $merchant, Cart $cart, User $user): array
    {
        $groupOfPackage = [];
        $groupOfPackages = [];

        /**  @var CartItem $item */
        foreach ($merchant->getItems() as $key => $item) {
            $offer = $item->getOffer();

            if ($offer->isDangerousProduct()) continue;

            $shippingTypeFromOffer = $offer->getTransportType();

            // sanatize shippingType
            $shippingTypeFromOffer = strtolower(trim($shippingTypeFromOffer));
            $shippingType = null;

            $shippingTypeAvailable = [
                'parcel' => UpelaApiService::PARCEL_SHIPMENT_TYPE,
                'pallet' => UpelaApiService::PALLET_SHIPMENT_TYPE,
            ];
            if (array_key_exists($shippingTypeFromOffer, $shippingTypeAvailable)) {
                $shippingType = $shippingTypeAvailable[$shippingTypeFromOffer];
            }

            if (!$shippingType) continue;

            // compute the shipment date for quotation based on the total preparation date of the item
            // the shipment date will be the week day of the preparation date in the current week + 7 days
            // if the given week day is already past than we take the same week day in the next week + 7 days
            $preparationdDate = $this->computeMerchantPreparationDate($item->getDeliveryTime());
            $realExpectedDeliveryDate = $preparationdDate;

            $shipmentDate = $preparationdDate;

            $limitPostponingDate = (new DateTimeImmutable())->modify('+ 14 day');

            if ($preparationdDate > $limitPostponingDate) {
                // Prepone date for quotation request

                // find the week day
                $preparationWeekDay = date('w', strtotime($preparationdDate->format('Y-m-d')));

                // retrieve the next week day and make it the shipment_date pour quotation
                $shipmentDate = new DateTime();
                $shipmentDate->modify('+ 1 week');

                $shipmentWeekDay = date('w', strtotime($shipmentDate->format('Y-m-d')));

                if ($shipmentWeekDay > $preparationWeekDay) {
                    $shipmentDate->modify(sprintf('- %d day', ($shipmentWeekDay - $preparationWeekDay)));
                } elseif ($shipmentWeekDay < $preparationWeekDay) {
                    $shipmentDate->modify(sprintf('+ %d day', ($preparationWeekDay - $shipmentWeekDay)));
                }
            }

            $deliveryDate = $shipmentDate->format('Y-m-d');
            $realExpectedDeliveryDate = $realExpectedDeliveryDate->format('Y-m-d');

            $realExpectedDeliveryDateString = strtotime($realExpectedDeliveryDate);
            $packages = $this->computePackage($offer, $item->getQuantity());

            $key = array_reduce(
                [
                    $offer->getFcaAddress(),
                    $offer->getFcaZipCode(),
                    $offer->getFcaZipTown(),
                    $realExpectedDeliveryDateString
                ],
                function($key, $element) {
                    $key .= strtolower(trim($element));
                    return $key;
                },
                ''
            );

            $hash = md5($key);

            /** @var Country $country */
            $country = $this->countryRepository->findOneBy(['izbFcaCountry' => $offer->getIncotermCountry()]);
            $countryCode = '';
            if (!is_null($country)) {
                $countryCode = strtoupper($country->getIzbergCode());
            }

            if (!empty($packages)) {
                if (count($packages) > 1) {
                    foreach ($packages as $element) {
                        $groupOfPackages[$hash]['result'][] = $element;
                    }
                } else {
                    $groupOfPackages[$hash]['result'][] = array_shift($packages);
                }
                $groupOfPackages[$hash]['item'][] = $item;
                $groupOfPackages[$hash]['offer'][] = $item;
                $groupOfPackages[$hash]['deliveryDate'] = $deliveryDate;
                $groupOfPackages[$hash]['street'] = $offer->getFcaAddress();
                $groupOfPackages[$hash]['zipCode'] = $offer->getFcaZipCode();
                $groupOfPackages[$hash]['town'] = $offer->getFcaZipTown();
                $groupOfPackages[$hash]['countryCode'] = $countryCode;
                $groupOfPackages[$hash]['shippingType'] = $shippingType;
            }
        }

        $shippingAddress = $cart->getAddress();
        $companyName = $user->getCompany()->getName();

        foreach ($groupOfPackages as $key => $group) {
            $groupOfPackage[$key] = (new GroupOfPackages())
                ->setShipmentDate($group['deliveryDate'])
                ->setMerchantId($merchant->getId())
                ->setMerchantName($merchant->getName())
                ->setMerchant($merchant)
                ->setUser($user)
                ->setCompanyName($companyName)
                ->setCountryCode($group['countryCode'])
                ->setStreet($group['street'])
                ->setTown($group['town'])
                ->setZipCode($group['zipCode'])
                ->setPackages($group['result'])
                ->setCartItems($group['item'])
                ->setShippingAddress($shippingAddress)
                ->setShipmentType($group['shippingType']);
        }

        return $groupOfPackage;
    }

    private function requestQuotation(GroupOfPackages $groupOfPackages): ArrayCollection
    {
        return $this->upelaApiService->getQuotation($groupOfPackages);
    }

    private function computeCartMerchantShippingOptionsV2(CartMerchant $merchant, Cart $cart, array $groupOfPackages, Company $company): ?CartMerchantShippingOptions
    {
        $currency = $cart->getCurrency();
        $merchantId = $merchant->getId();
        $cheapestOptions = [];
        $fastestOptions = [];
        $monoCarrierOption = null;
        $fastestOption = null;
        $cheapestOption = null;
        $cartMerchantShippingOptions = null;

        $cheapestOptions['price'] = null;
        $cheapestOptions['data'] = null;
        $fastestOptions['price'] = null;
        $fastestOptions['data'] = null;

        /**
         * @var GroupOfPackages $group
         */
        foreach ($groupOfPackages as $key => $group) {

            $carrierOffers = new ArrayCollection();
            $carrierOffer = new CarrierOffer();
            $carrierOffer->setPrice(0);
            $carrierOffer->setCartItems($group->getCartItems());
            $carrierOffer->setItemsQte($group->getItemsQte());
            $carrierOffer->setGroupOfPackages($group);
            $carrierOffer->setShipmentId(1); // @TODO
            $carrierOffers->add($carrierOffer);

            $cheapestCarrierOffer = $carrierOffer;
            $fastestCarrierOffer = $carrierOffer;

            $cheapestOptions['data'][] = $cheapestCarrierOffer;
            $cheapestOptions['price'] = $cheapestOptions['price'] + $cheapestCarrierOffer->getPrice();
            $fastestOptions['data'][] = $fastestCarrierOffer;
            $fastestOptions['price'] = $fastestOptions['price'] + $fastestCarrierOffer->getPrice();

        }

        if ($cheapestOptions['data']) {
            $cheapestOption = (new ShippingOption())
                ->setCarrierOffers($cheapestOptions['data'])
                ->setPrice($cheapestOptions['price']);
        }

        if (isset($fastestOptions['data'])) {
            $fastestOption = (new ShippingOption())
                ->setCarrierOffers($fastestOptions['data'])
                ->setPrice($fastestOptions['price']);
        }

        if ($cheapestOption || $fastestOption) {
            $shippingVatRate  = 0;
            $feesOperator = 0;

            $reviewShippingOptionPrice = function (?ShippingOption $shippingOption) use ($shippingVatRate, $feesOperator): ?ShippingOption {
                if (!$shippingOption) {
                    return $shippingOption;
                }

                $carrierOffers = $shippingOption->getCarrierOffers();
                $carrierOffers = array_map(
                    function (CarrierOffer $carrierOffer) use ($shippingVatRate, $feesOperator){
                        $carrierOffer
                            ->setUpelaVatPrice(0)
                            ->setUpelaPriceTe(0)
                            ->setUpelaPriceTi(0)
                            ->setPriceTe(0)
                            ->setPriceTi(0)
                            ->setPrice(0)
                            ->setVatPrice(0);

                        return $carrierOffer;
                    },
                    $carrierOffers
                );

                $price = array_reduce(
                    $carrierOffers,
                    function(float $price, CarrierOffer $carrierOffer) {
                        return $price + $carrierOffer->getPrice();
                    },
                    0.0
                );

                $shippingVatPrice = array_reduce(
                    $carrierOffers,
                    function($shippingVatPrice, CarrierOffer $carrierOffer) {
                        return $shippingVatPrice + $carrierOffer->getVatPrice();
                    },
                    0.0
                );

                $shippingOption
                    ->setCarrierOffers($carrierOffers)
                    ->setPrice($price)
                    ->setVatRate($shippingVatRate)
                    ->setVatPrice($shippingVatPrice);

                return $shippingOption;
            };

            // review shipment date and delivery date for every options
            $reviewShippingOptionDates = function (?ShippingOption $shippingOption) : ?ShippingOption{

                if (!$shippingOption) {
                    return $shippingOption;
                }

                $carrierOffers = $shippingOption->getCarrierOffers();

                $carrierOffers = array_map(
                    function (CarrierOffer $carrierOffer) {
                        $items = $carrierOffer->getCartItems();
                        if (empty($items)) {
                            throw new Exception('CarrierOffer should have cart items');
                        }

                        /** @var CartItem $item */
                        $item = current($items);

                        $updatedShipmentDate = $this->computeMerchantPreparationDate($item->getDeliveryTime());

                        $shipmentDate = new DateTimeImmutable($carrierOffer->getShipmentDate());
                        $deliveryDate = new DateTimeImmutable($carrierOffer->getDeliveryDate());

                        $updatedDeliveryDate = $updatedShipmentDate->add($shipmentDate->diff($deliveryDate));

                        $carrierOffer->setShipmentDate($updatedShipmentDate->format('Y-m-d'));
                        $carrierOffer->setDeliveryDate($updatedDeliveryDate->format('Y-m-d'));

                        return $carrierOffer;
                    },
                    $carrierOffers
                );

                $shippingOption->setCarrierOffers($carrierOffers);

                return $shippingOption;
            };

            // Shipping option dates
            $cheapestOption = call_user_func($reviewShippingOptionDates, $cheapestOption);
            $fastestOption = call_user_func($reviewShippingOptionDates, $fastestOption);


            // tax rates
            $cheapestOption = call_user_func($reviewShippingOptionPrice, $cheapestOption);
            $fastestOption = call_user_func($reviewShippingOptionPrice, $fastestOption);

            $cartMerchantShippingOptions = (new CartMerchantShippingOptions())
                ->setMerchantId($merchantId)
                ->setCartId($cart->getId())
                ->setCheapestOption($cheapestOption)
                ->setFastestOption($fastestOption)
                ->setMonoCarrierOption($monoCarrierOption)
                ->setVatRate($shippingVatRate);
        }

        return $cartMerchantShippingOptions;
    }

    private function computeCartMerchantShippingOptions(CartMerchant $merchant, Cart $cart, array $groupOfPackages, Company $company): ?CartMerchantShippingOptions
    {
        $currency = $cart->getCurrency();
        $merchantId = $merchant->getId();
        $excludeOptionId = [];
        $allOffers = [];
        $cheapestOptions = [];
        $fastestOptions = [];
        $monoCarrierOptions = [];
        $monoCarrierOption = null;
        $fastestOption = null;
        $cheapestOption = null;
        $cartMerchantShippingOptions = null;

        $cheapestOptions['price'] = null;
        $cheapestOptions['data'] = null;
        $fastestOptions['price'] = null;
        $fastestOptions['data'] = null;

        /**
         * @var GroupOfPackages $groupOfPackage
         */
        foreach ($groupOfPackages as $key => $group) {

            $carrierOffers = $this->requestQuotation($group);
            if(!$carrierOffers->isEmpty()) {

                foreach ($carrierOffers as $carrierOffer) {
                    $allOffers[$key][$carrierOffer->getCarrierIdentifier()] = $carrierOffer;
                }

                $cheapestCarrierOffer = $this->computeCheapestShippingOffers($carrierOffers->getIterator());
                $fastestCarrierOffer = $this->computeFastestShippingOffers($carrierOffers->getIterator());

                $excludeOptionId[] = $cheapestCarrierOffer->getCarrierIdentifier();
                $excludeOptionId[] = $fastestCarrierOffer->getCarrierIdentifier();

                $cheapestOptions['data'][] = $cheapestCarrierOffer;
                $cheapestOptions['price'] = $cheapestOptions['price'] + $cheapestCarrierOffer->getPrice();
                $fastestOptions['data'][] = $fastestCarrierOffer;
                $fastestOptions['price'] = $fastestOptions['price'] + $fastestCarrierOffer->getPrice();
            }
        }

        // HIDE MONO CARRIER OPTIONS FOR NOW
        /************************************
        $excludeOptionId = array_unique($excludeOptionId);
        if (!empty($allOffers)) {
            $monoCarrierOptions = $this->computeMonoCarrierShippingOffers($allOffers, $excludeOptionId);
        }
        *************************************/

        if ($cheapestOptions['data']) {
            $cheapestOption = (new ShippingOption())
                ->setCarrierOffers($cheapestOptions['data'])
                ->setPrice($cheapestOptions['price']);
        }

        if (isset($fastestOptions['data'])) {
            $fastestOption = (new ShippingOption())
                ->setCarrierOffers($fastestOptions['data'])
                ->setPrice($fastestOptions['price']);
        }

        // HIDE MONO CARRIER OPTIONS FOR NOW
        /************************************
        if (count($monoCarrierOptions) > 1) {
            foreach ($monoCarrierOptions as $monoCarrier) {
                $price = array_reduce(
                    $monoCarrier,
                    function ($price, CarrierOffer $carrierOffer) {
                        return $price + $carrierOffer->getPrice();
                    },
                    0
                );

                $monoCarrierOption[] = (new ShippingOption())
                    ->setCarrierOffers($monoCarrier)
                    ->setPrice($price);
            }
        }
        **************************************/
        // HIDE MONO CARRIER OPTIONS FOR NOW
        // if ($cheapestOption || $fastestOption || $monoCarrierOption) {
        if ($cheapestOption || $fastestOption) {
            // review taxRate and priceTi for every shipping options
            // add fees operator to price Te for every shipping options
            $shippingVatRate  = $this->customsService->shippingTaxRateToApply($company, $merchantId);
            $feesOperator = $this->feesOperatorShippingService->findByCurrency($currency);

            $reviewShippingOptionPrice = function (?ShippingOption $shippingOption) use ($shippingVatRate, $feesOperator): ?ShippingOption {
                if (!$shippingOption) {
                    return $shippingOption;
                }
                $carrierOffers = $shippingOption->getCarrierOffers();

                $carrierOffers = array_map(
                    function (CarrierOffer $carrierOffer) use ($shippingVatRate, $feesOperator){
                        $vatPrice = 0;
                        $upelaVatPrice = 0;
                        $carrierOffer->setVatRate($shippingVatRate);

                        $upelaPriceTe = $carrierOffer->getPriceTe();
                        $upelaPriceTi = $upelaPriceTe;
                        if ($shippingVatRate) {
                            $upelaVatPrice = round($upelaPriceTi * $shippingVatRate / 100, 2);
                            $upelaPriceTi = $upelaPriceTi + $upelaVatPrice;
                        }

                        $priceTe = $upelaPriceTe;
                        $priceFeesOperator = ($feesOperator > 0) ? round($priceTe * $feesOperator / 100, 2) : 0;
                        $priceTe = $priceTe + $priceFeesOperator;
                        $priceTi = $priceTe;

                        if ($shippingVatRate) {
                            $vatPrice = round($priceTi * $shippingVatRate / 100, 2);
                            $priceTi = $priceTi + $upelaVatPrice;
                        }

                        $carrierOffer
                            ->setUpelaVatPrice($upelaVatPrice)
                            ->setUpelaPriceTe($upelaPriceTe)
                            ->setUpelaPriceTi($upelaPriceTi)
                            ->setPriceTe($priceTe)
                            ->setPriceTi($priceTi)
                            ->setPrice($priceTe)
                            ->setVatPrice($vatPrice);

                        return $carrierOffer;
                    },
                    $carrierOffers
                );

                $price = array_reduce(
                    $carrierOffers,
                    function(float $price, CarrierOffer $carrierOffer) {
                        return $price + $carrierOffer->getPrice();
                    },
                    0.0
                );

                $shippingVatPrice = array_reduce(
                    $carrierOffers,
                    function($shippingVatPrice, CarrierOffer $carrierOffer) {
                        return $shippingVatPrice + $carrierOffer->getVatPrice();
                    },
                    0.0
                );

                $shippingOption
                    ->setCarrierOffers($carrierOffers)
                    ->setPrice($price)
                    ->setVatRate($shippingVatRate)
                    ->setVatPrice($shippingVatPrice);

                return $shippingOption;
            };

            // review shipment date and delivery date for every options
            $reviewShippingOptionDates = function (?ShippingOption $shippingOption) : ?ShippingOption{

                if (!$shippingOption) {
                    return $shippingOption;
                }

                $carrierOffers = $shippingOption->getCarrierOffers();

                $carrierOffers = array_map(
                    function (CarrierOffer $carrierOffer) {
                        $items = $carrierOffer->getCartItems();
                        if (empty($items)) {
                            throw new Exception('CarrierOffer should have cart items');
                        }

                        /** @var CartItem $item */
                        $item = current($items);

                        $updatedShipmentDate = $this->computeMerchantPreparationDate($item->getDeliveryTime());

                        $shipmentDate = new DateTimeImmutable($carrierOffer->getShipmentDate());
                        $deliveryDate = new DateTimeImmutable($carrierOffer->getDeliveryDate());

                        $updatedDeliveryDate = $updatedShipmentDate->add($shipmentDate->diff($deliveryDate));

                        $carrierOffer->setShipmentDate($updatedShipmentDate->format('Y-m-d'));
                        $carrierOffer->setDeliveryDate($updatedDeliveryDate->format('Y-m-d'));

                        return $carrierOffer;
                    },
                    $carrierOffers
                );

                $shippingOption->setCarrierOffers($carrierOffers);

                return $shippingOption;
            };

            // Shipping option dates
            $cheapestOption = call_user_func($reviewShippingOptionDates, $cheapestOption);
            $fastestOption = call_user_func($reviewShippingOptionDates, $fastestOption);

            // HIDE MONO CARRIER OPTIONS FOR NOW
            /************************************
            if ($monoCarrierOption) {
                $monoCarrierOption = array_map(
                    function (ShippingOption $monoCarrierShippingOption) use ($reviewShippingOptionDates) {
                        return call_user_func(
                            $reviewShippingOptionDates,
                            $monoCarrierShippingOption
                        );
                    },
                    $monoCarrierOption
                );
            }
             **************************************/



            // tax rates
            $cheapestOption = call_user_func($reviewShippingOptionPrice, $cheapestOption);
            $fastestOption = call_user_func($reviewShippingOptionPrice, $fastestOption);
            // HIDE MONO CARRIER OPTIONS FOR NOW
            /************************************
            if ($monoCarrierOption) {
                $monoCarrierOption = array_map(
                    function (ShippingOption $monoCarrierShippingOption) use ($reviewShippingOptionPrice) {
                        return call_user_func(
                            $reviewShippingOptionPrice,
                            $monoCarrierShippingOption
                        );
                    },
                    $monoCarrierOption
                );
            }
             * **************************************/

            $cartMerchantShippingOptions = (new CartMerchantShippingOptions())
                ->setMerchantId($merchantId)
                ->setCartId($cart->getId())
                ->setCheapestOption($cheapestOption)
                ->setFastestOption($fastestOption)
                ->setMonoCarrierOption($monoCarrierOption)
                ->setVatRate($shippingVatRate);
        }

        return $cartMerchantShippingOptions;
    }

    public function computePackage(Offer $offer, int $quantity): ?array
    {
        $packages = [];

        // MOQ + Batchsize
        if (!empty($offer->getPackageWeight()) && // packageWeight, Length, Height, Width are for batch size dimension
            !empty($offer->getPackageLength()) &&
            !empty($offer->getPackageHeight()) &&
            !empty($offer->getPackageWidth()) &&
            !empty($offer->getPackageWeightMoq()) && // PackageWeightMoq, LengthMoq, HeightMoq, WidthMoq are for moq dimension
            !empty($offer->getPackageLengthMoq()) &&
            !empty($offer->getPackageHeightMoq()) &&
            !empty($offer->getPackageWidthMoq()) &&
            !empty($offer->getBatchSize()) &&
            !empty($offer->getMoq())
        ) {

            $moqQuantity = intdiv($quantity, $offer->getMoq());

            if ($moqQuantity > 0) {
                $packages[] = $this->buildPackage(
                    $moqQuantity,
                    $offer->getPackageWeightMoq(),
                    $offer->getPackageWidthMoq(),
                    $offer->getPackageHeightMoq(),
                    $offer->getPackageLengthMoq()
                );
            }

            $batchSizeQuantity = ($quantity % $offer->getMoq());

            if ($batchSizeQuantity > 0) {
                $batchSizeQuantity = intdiv($batchSizeQuantity, $offer->getBatchSize());
                $packages[] = $this->buildPackage(
                    $batchSizeQuantity,
                    $offer->getPackageWeight(),
                    $offer->getPackageWidth(),
                    $offer->getPackageHeight(),
                    $offer->getPackageLength()
                );
            }

        // Batchsize
        } elseif (
            !empty($offer->getPackageWeight()) &&
            !empty($offer->getPackageLength()) &&
            !empty($offer->getPackageHeight()) &&
            !empty($offer->getPackageWidth()) &&
            !empty($offer->getBatchSize()) &&
            ($quantity % $offer->getBatchSize()) === 0
        ) {
            $batchSizeQuantity = intdiv($quantity, $offer->getBatchSize());

            if ($batchSizeQuantity > 0) {
                $packages[] = $this->buildPackage(
                    $batchSizeQuantity,
                    $offer->getPackageWeight(),
                    $offer->getPackageWidth(),
                    $offer->getPackageHeight(),
                    $offer->getPackageLength()
                );
            }

        // MOQ
        } elseif (
            !empty($offer->getPackageWeightMoq()) &&
            !empty($offer->getPackageLengthMoq()) &&
            !empty($offer->getPackageHeightMoq()) &&
            !empty($offer->getPackageWidthMoq()) &&
            ($quantity % $offer->getMoq()) === 0 // product quantity should be multiple of Moq
        ) {
            $moqQuantity = intdiv($quantity, $offer->getMoq());

            $packages[] = $this->buildPackage(
                $moqQuantity,
                $offer->getPackageWeightMoq(),
                $offer->getPackageWidthMoq(),
                $offer->getPackageHeightMoq(),
                $offer->getPackageLengthMoq()
            );
        }

        return $packages;
    }

    private function buildPackage(int $number, $weight, $width, $height, $length): array
    {
        return [
            'number' => $number,
            'weight' => $weight,
            "x" => $width,
            "y" => $height,
            "z" => $length
        ];
    }

    private function sortOffersByPrice(ArrayIterator $carrierOfferIterator): ArrayIterator
    {
        $carrierOfferIterator->uasort(function ($first, $second) {
            return $first->getPrice() > $second->getPrice() ? 1 : -1;
        });
        return $carrierOfferIterator;
    }

    private function computeCheapestShippingOffers(ArrayIterator $carrierOfferIterator): CarrierOffer
    {
        return $carrierOfferIterator->current();
    }

    private function computeFastestShippingOffers(ArrayIterator $carrierOfferIterator): CarrierOffer
    {
        $carrierOfferIterator->uasort(function (CarrierOffer $first, CarrierOffer $second) {
            return strtotime($first->getDeliveryDate()) > strtotime($second->getDeliveryDate()) ? 1 : -1;
        });

        return $carrierOfferIterator->current();
    }

    private function computeMonoCarrierShippingOffers(array $allOffers, array $excludeOffersId): array
    {
        $monoCarrierOffers = [];
        if (!empty($allOffers)) {
            $allKeys = array_keys($allOffers);
            $length = count($allOffers);
            if ($length > 1) {
                for ($i = 0; $i < $length; $i++) {
                    foreach ($allOffers[$allKeys[$i]] as $key => $upelaOffer) {
                        if (isset($allKeys[$i + 1])) {
                            if (array_key_exists($key, $allOffers[$allKeys[$i + 1]])) {
                                $monoCarrierOffers[$key][] = $upelaOffer;
                                $monoCarrierOffers[$key][] = $allOffers[$allKeys[$i + 1]][$key];
                            }
                        }

                    }
                }
            } else {
                for ($i = 0; $i < $length; $i++) {
                    foreach ($allOffers[$allKeys[$i]] as $key => $upelaOffer) {
                        $monoCarrierOffers[$key][] = $upelaOffer;
                    }
                }
            }

            foreach ($monoCarrierOffers as $key => $carrierOffers) {
                if (count($monoCarrierOffers[$key]) !== count($allOffers)) {
                    unset($monoCarrierOffers[$key]);
                }
                $monoCarrierOffers[$key] = $carrierOffers;
                if (in_array($key, $excludeOffersId)) {
                    unset($monoCarrierOffers[$key]);
                }
            }
        }


        uasort($monoCarrierOffers, function (array $first, array $second) {
            // compute price of first
            $firstPrice = array_reduce(
                $first,
                function ($firstPrice, CarrierOffer $carrierOffer) {
                    return $firstPrice + $carrierOffer->getPrice();
                },
                0
            );

            // compute price of second
            $secondPrice = array_reduce(
                $second,
                function ($secondPrice, CarrierOffer $carrierOffer) {
                    return $secondPrice + $carrierOffer->getPrice();
                },
                0
            );

            return $firstPrice > $secondPrice ? 1 : -1;
        });

        return array_slice($monoCarrierOffers, 0, 3);
    }

    public function deleteCache(int $userId, int $catId, int $merchantId, ?int $addressId = null)
    {
        $addressId = $addressId ?? 0;
        $cacheKey = $this->buildCacheKey($userId, $catId, $merchantId, $addressId);
        try {
            $this->cache->removeItem($cacheKey);

        } catch (Exception $e) {
            $this->logger->error($e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::TECHNICAL_ERROR
                ]));
        }
    }

    private function buildCacheKey(int $userId, int $cartId, int $merchantId, int $adressId = 0): string
    {
        return self::CART_MERCHANT_SHIPPING_OPTIONS . '_' . $userId . '_' . $cartId . '_' . $merchantId . '_' .$adressId;
    }

    /**
     * @param int $shippingProductId
     * @return $this
     */
    public function setShippingProductId(int $shippingProductId): self
    {
        $this->shippingProductId = $shippingProductId;
        return $this;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
