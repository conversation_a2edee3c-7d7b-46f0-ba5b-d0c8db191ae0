
{% form_theme form 'Form/appli_layout.html.twig' %}

{% extends '@OpenFront/base.html.twig' %}

{% block body %}
    {% block fos_user_content %}

<img class="Page-inner">

    <img class="register-background" src="{{ asset('images/landing-background.png') }}">

    <div class="form-register">
        <div class="form-title">
                <h1>
                    {{ 'registration.title'|trans }} :
                </h1>
                <p>{{ 'registration.subtitle'|trans }}</p>
        </div>

        {% if merchant is defined and merchant == true %}
            {% set path_form = 'front.merchant.registration' %}
            <p class="merchant-ask-login" style="margin-bottom: 0">{{ 'merchant.registration.areYouMemberSeller'|trans }} <a href="{{ seller_domain_url }}" target="_blank" class="primary">{{ 'merchant.registration.login'|trans }}</a></p>
            <p class="merchant-ask-login">{{ 'merchant.registration.youAreBuyer'|trans }} <a href="{{ path('fos_user_registration_register') }}" class="primary">{{ 'merchant.registration.clickHereCreate'|trans }}</a> / <a href="{{ path('front.login') }}" class="primary">{{ 'merchant.registration.login'|trans }}</a></p>
        {% else %}
            {% set path_form = 'fos_user_registration_register' %}
            <p class="merchant-ask-login" style="margin-bottom: 0">{{ 'merchant.registration.areYouMemberBuyer'|trans }} <a href="{{ path('front.login') }}" class="primary">{{ 'merchant.registration.login'|trans }}</a></p>
            <p class="merchant-ask-login">{{ 'merchant.registration.youAreSeller'|trans }} <a href="{{ path('front.merchant.registration') }}" class="primary">{{ 'merchant.registration.clickHereCreate'|trans }}</a> / <a href="{{ seller_domain_url }}" target="_blank" class="primary">{{ 'merchant.registration.login'|trans }}</a></p>
        {% endif %}
        {{ form_start(form, {'method': 'post', 'action': path(path_form), 'attr': {'class': 'fos_user_registration_register', 'id':'js-form-register', 'autocomplete':'off'}}) }}

        <div class="form-row">
            <div class="js-select-wrapper select-wrapper has-text" style="margin-bottom: 0px;">
                {{ form_widget(form.type) }}
                {{ form_label(form.type) }}
            </div>
        </div>

        <div class="form-fields">

            <div class="form-row" style="margin-bottom: 30px;">
                <div class="js-select-wrapper select-wrapper has-text">
                    {{ form_widget(form.country, {'attr':{'id':'fos_user_registration_form_country', 'data-sort': 'true'}}) }}
                    <div class="label-tooltip">
                        {{ form_label(form.country) }}
                        <div class="tooltip">
                            <svg class="Icon">
                                <use xlink:href="#icon-help"></use>
                            </svg>
                            <div class="tooltiptext">
                                {{ 'buyer.registration.no_country_in_list'|trans }}
                            </div>
                        </div>
                    </div>
                </div>
                <small class="error">{{ form_errors(form.country) }}</small>
            </div>

            {% if merchant is defined and merchant == true %}
                <div class="form-row">
                    <div class="js-select-wrapper select-wrapper has-text">
                        {{ form_widget(form.currency, {'attr':{'id':'fos_user_registration_form_currency', 'data-sort': 'true'}}) }}
                        {{ form_label(form.currency) }}
                    </div>
                    <small class="error">{{ form_errors(form.currency) }}</small>
                </div>
            {% endif %}

            {{ form_row(form.raisonSociale, {'attr':{'maxlength':'50'}}) }}
            {{ form_errors(form.raisonSociale) }}

            {{ form_row(form.identification) }}

            {{ form_row(form.lastname) }}
            {{ form_errors(form.lastname) }}

            {{ form_row(form.firstname) }}
            {{ form_errors(form.firstname) }}

            {% if merchant is not defined or merchant == false %}
                {{ form_row(form.function) }}
            {% else %}
                {% do form.function.setRendered %}
            {% endif %}

            {{ form_row(form.mainPhoneNumber) }}

            {{ form_row(form.email) }}

            <div>
                {{ form_row(form.plainPassword) }}
                <div class="password-help">
                    {{ 'buyer.registration.password_help'|trans }}
                </div>
            </div>

            {{ form_row(form.recaptcha) }}
        </div>
        <button id="js-submit-button" type="submit" value="{{ 'buyer.registration.submit'|trans }}">
            {{ 'buyer.registration.submit'|trans }}
        </button>
        {{ form_end(form) }}

        {% if merchant is defined and merchant == true %}
            <p class="accept-terms">{{ 'merchant.registration.acceptTerms'|trans }}</p>
        {% endif %}
    </div>

<script>
  document.addEventListener('DOMContentLoaded', function() {

      {% if app.request.get('isRedirect') is not null %}
        {% if merchant is not defined or merchant == false %}
            $('#fos_user_registration_form_type option[value=buyer]').attr('selected','selected');
        {% else %}
            $('#fos_user_registration_form_type option[value=vendor]').attr('selected','selected');
        {% endif %}
      {% endif %}

    var $form = $('#js-form-register');

    {% if captcha_enabled %}
        window.UI.ReCaptcha.init('fos_user_registration_form_hiddenRecaptcha', 'js-submit-button');
    {% endif %}

      // Fix label display for Chrome saved data autofill
      setTimeout(function() {
         $('input:-webkit-autofill').each(function() {
             $(this).addClass('has-text');
          });
      }, 100);

      // Initialize the custom company identification validation
    window.UI.CompanyIdentification.init(
    '#fos_user_registration_form_country'
    );

    // Initialize country selection
    window.UI.CountrySelector.init(
      '#fos_user_registration_form_country',
      '#fos_user_registration_form_identification'
    );

    window.UI.Select.init();

    {{ form_jquery_validation(form) }}

    // Hack
    $('#fos_user_registration_form_identification').parent('.form-row').addClass('CompanyIdentification');

    $('#fos_user_registration_form_type').change(function() {
        {% if merchant is not defined or merchant == false %}
            if($(this).find(":selected").val() === 'vendor'){
                window.location.href = "{{ path('front.merchant.registration', {'isRedirect':'true'}) }}";
            }
        {% else %}
            if($(this).find(":selected").val() === 'buyer'){
                window.location.href = "{{ path('fos_user_registration_register', {'isRedirect':'true'}) }}";
            }
        {% endif %}
    });


    // Hack phone numbers pattern message
    $form.data('validator').settings.messages['fos_user_registration_form[mainPhoneNumber]'].pattern = '{{ 'form.contact.phone_regex'|trans({}, 'validators') }}';

      var identErrorTxt = '{{ 'registration.error.identification_already_used'|trans({}, 'AppBundle') }}';
      if($(".CompanyIdentification .error ul li").text() === identErrorTxt){
          window.UI.Modal.confirm('', "{{ 'registration.error.identification_already_used_alert'|trans({}, 'AppBundle')|raw }}", function () {
                  window.UI.Modal.showLoading();
                  window.location.href = "{{ path('anonymous.ticket.create') }}"
              },
              function () {
              });
      }
  });
</script>

    {% endblock fos_user_content %}
{% endblock %}
